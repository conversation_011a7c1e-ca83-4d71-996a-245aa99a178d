
type = string-attribute
name = TYPESTRING
count = 15
'3DIM_HEAD_ANAT~

type = string-attribute
name = IDCODE_STRING
count = 27
'AFN_vLKn9e5VumKelWXNeq4SWA~

type = string-attribute
name = IDCODE_DATE
count = 25
'Tue Jan 23 20:05:10 2018~

type = integer-attribute
name = SCENE_DATA
count = 8
 2 2 0 -999 -999
 -999 -999 -999

type = string-attribute
name = LABEL_1
count = 5
'zyxt~

type = string-attribute
name = LABEL_2
count = 5
'zyxt~

type = string-attribute
name = DATASET_NAME
count = 5
'zyxt~

type = integer-attribute
name = ORIENT_SPECIFIC
count = 3
 1 2 4

type  = float-attribute
name  = ORIGIN
count = 3
             66             87            -54

type  = float-attribute
name  = DELTA
count = 3
             -3             -3              3

type  = float-attribute
name  = IJK_TO_DICOM
count = 12
             -3              0              0             66              0
             -3              0             87              0              0
              3            -54

type  = float-attribute
name  = IJK_TO_DICOM_REAL
count = 12
             -3              0              0             66              0
             -3              0             87              0              0
              3            -54

type  = float-attribute
name  = BRICK_STATS
count = 2
   1.941682e-07    0.001272461

type = integer-attribute
name = DATASET_RANK
count = 8
 3 1 0 0 0
 0 0 0

type = integer-attribute
name = DATASET_DIMENSIONS
count = 5
 47 54 43 0 0

type = integer-attribute
name = BRICK_TYPES
count = 1
 1

type  = float-attribute
name  = BRICK_FLOAT_FACS
count = 1
   3.883363e-08

type = string-attribute
name = BRICK_LABS
count = 3
'#0~

type = string-attribute
name = BRICK_KEYWORDS
count = 1
'~

type = string-attribute
name = TEMPLATE_SPACE
count = 5
'TLRC~

type = integer-attribute
name = INT_CMAP
count = 1
 0

type = string-attribute
name = BYTEORDER_STRING
count = 10
'LSB_FIRST~
