.. -*- mode: rst; fill-column: 78 -*-
.. ex: set sts=4 ts=4 sw=4 et tw=79:
.. vim:syntax=rest

.. _license:

**********************
Copyright and Licenses
**********************

NiBabel
=======

The nibabel package, including all examples, code snippets and attached
documentation is covered by the MIT license.

::

  The MIT License

  Copyright (c) 2009-2019 <PERSON> <<EMAIL>>
  Copyright (c) 2010-2013 <PERSON> <****************>
  Copyright (c) 2006-2014 <PERSON> <<EMAIL>>
  Copyright (c) 2011 <PERSON> <<EMAIL>>
  Copyright (c) 2010-2011 <PERSON><PERSON><PERSON> <<EMAIL>>
  Copyright (c) 2011-2019 <PERSON><PERSON><PERSON> <<EMAIL>>
  Copyright (c) 2015-2019 <PERSON> <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to deal
  in the Software without restriction, including without limitation the rights
  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
  THE SOFTWARE.


3rd party code and data
=======================

Some code distributed within the nibabel sources was developed by other
projects. This code is distributed under its respective licenses that are
listed below.

NetCDF
------

The netcdf IO module has been taken from SciPy.

::

  Copyright (c) 1999-2010 SciPy Developers <<EMAIL>>

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

    a. Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.
    b. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
    c. Neither the name of the Enthought nor the names of its contributors
       may be used to endorse or promote products derived from this software
       without specific prior written permission.


  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE FOR
  ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
  DAMAGE.


Sphinx autosummary extension
----------------------------

This extension has been copied from NumPy (Jul 16, 2010) as the one shipped with
Sphinx 0.6 doesn't work properly.

::

  Copyright (c) 2007-2009 Stefan van der Walt and Sphinx team

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

    a. Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.
    b. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
    c. Neither the name of the Enthought nor the names of its contributors
       may be used to endorse or promote products derived from this software
       without specific prior written permission.


  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE FOR
  ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
  DAMAGE.

OrderedSet
-----------

In ``nibabel/externals/oset.py``

Copied from: https://files.pythonhosted.org/packages/d6/b1/a49498c699a3fda5d635cc1fa222ffc686ea3b5d04b84a3166c4cab0c57b/oset-0.1.3.tar.gz

::

    Copyright (c) 2009, Raymond Hettinger, and others All rights reserved.

    Package structured based on the one developed to odict Copyright (c) 2010, BlueDynamics Alliance, Austria

    - Redistributions of source code must retain the above copyright notice, this
      list of conditions and the following disclaimer.

    - Redistributions in binary form must reproduce the above copyright notice, this
      list of conditions and the following disclaimer in the documentation and/or
      other materials provided with the distribution.

    - Neither the name of the BlueDynamics Alliance nor the names of its
      contributors may be used to endorse or promote products derived from this
      software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY BlueDynamics Alliance AS IS AND ANY EXPRESS OR
    IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
    SHALL BlueDynamics Alliance BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
    SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
    BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
    CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
    IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
    OF SUCH DAMAGE.

mni_icbm152_t1_tal_nlin_asym_09a
--------------------------------

The file ``doc/source/someone.nii.gz`` is a subsampled version of the file
``mni_icbm152_t1_tal_nlin_asym_09a.nii`` from the MNI non-linear templates
archive ``mni_icbm152_t1_tal_nlin_asym_09a``.  The original image has the
following license (where 'softwware' refers to the image):

::

    Copyright (C) 1993-2004 Louis Collins, McConnell Brain Imaging Centre,
    Montreal Neurological Institute, McGill University.

    Permission to use, copy, modify, and distribute this software and its
    documentation for any purpose and without fee is hereby granted, provided
    that the above copyright notice appear in all copies.  The authors and
    McGill University make no representations about the suitability of this
    software for any purpose.  It is provided "as is" without express or
    implied warranty.  The authors are not responsible for any data loss,
    equipment damage, property loss, or injury to subjects or patients
    resulting from the use or misuse of this software package.

Philips PAR/REC data
--------------------

The files::

    nibabel/tests/data/phantom_EPI_asc_CLEAR_2_1.PAR
    nibabel/tests/data/phantom_EPI_asc_CLEAR_2_1.REC
    nibabel/tests/data/Phantom_EPI_3mm_cor_20APtrans_15RLrot_SENSE_15_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_cor_SENSE_8_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_sag_15AP_SENSE_13_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_sag_15FH_SENSE_12_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_sag_15RL_SENSE_11_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_sag_SENSE_7_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_tra_-30AP_10RL_20FH_SENSE_14_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_tra_15FH_SENSE_9_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_tra_15RL_SENSE_10_1.PAR
    nibabel/tests/data/Phantom_EPI_3mm_tra_SENSE_6_1.PAR

are from http://psydata.ovgu.de/philips_achieva_testfiles, and released under
the PDDL version 1.0 available at http://opendatacommons.org/licenses/pddl/1.0/

The files::

    nibabel/nibabel/tests/data/DTI.PAR
    nibabel/nibabel/tests/data/NA.PAR
    nibabel/nibabel/tests/data/T1.PAR
    nibabel/nibabel/tests/data/T2-interleaved.PAR
    nibabel/nibabel/tests/data/T2.PAR
    nibabel/nibabel/tests/data/T2_-interleaved.PAR
    nibabel/nibabel/tests/data/T2_.PAR
    nibabel/nibabel/tests/data/fieldmap.PAR

are from https://github.com/yarikoptic/nitest-balls1, also released under the
the PDDL version 1.0 available at http://opendatacommons.org/licenses/pddl/1.0/

    nibabel/nibabel/tests/data/umass_anonymized.PAR

is courtesy of the University of Massachusetts Medical School, also released
under the PDDL.
