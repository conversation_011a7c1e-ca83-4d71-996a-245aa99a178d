../../Scripts/nib-conform.exe,sha256=3CK2s79Zi-ZOWFP3cfDfzgE2m6-EXmFhaxFNsEKrdEw,108426
../../Scripts/nib-convert.exe,sha256=MdbImZdI4_oSRPhlKUAMSh3R_PJ8ZCWQNwu9F-IOZ_g,108426
../../Scripts/nib-dicomfs.exe,sha256=X4fJaC0aZBK0jy2e50-6y-I-LKxsvU0r-qH2XU_3S8Q,108426
../../Scripts/nib-diff.exe,sha256=VnS9tXgob02J-ffhA-uV1ZYbtAaS1BStG95rjpGufSw,108423
../../Scripts/nib-ls.exe,sha256=gK6ZusNQLwQG_skGfaoUzXEFX9evdzzLv-zo0nvh8p8,108421
../../Scripts/nib-nifti-dx.exe,sha256=_tzcmqN13E0wN14bc56erpGnXq32tUj597Ow3Py_WXY,108427
../../Scripts/nib-roi.exe,sha256=LiJPaqQYFrmsy74B9S3gmmFfVvkl1rRrMAFuDnYFNSU,108422
../../Scripts/nib-stats.exe,sha256=WxerCp1R5YukRG0HZ7xOjFp2LwHHTKnkajgkGHv-ErY,108424
../../Scripts/nib-tck2trk.exe,sha256=ymSlvAu2hfLcJXbBdrp0ruoCeo4dRBIMolr9IfugQGA,108426
../../Scripts/nib-trk2tck.exe,sha256=uRgw7SLsktyhdgkRYYEdjp4xL93cbridhOegvagxBpE,108426
../../Scripts/parrec2nii.exe,sha256=WxWyLDkg6dcseWTlph6tTnyAQ1gvmDSfLVbAUeT90Us,108429
nibabel-5.3.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
nibabel-5.3.2.dist-info/METADATA,sha256=EhLTU0TFxX9v9sQfQRtlxAmsTXwl_2Caj_RJK3zamvA,9085
nibabel-5.3.2.dist-info/RECORD,,
nibabel-5.3.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel-5.3.2.dist-info/WHEEL,sha256=1yFddiXMmvYK7QYTqtRNtX66WJ0Mz8PYEiEUoOUUxRY,87
nibabel-5.3.2.dist-info/entry_points.txt,sha256=raA8Pq4Z1flFb77vleH2GcCL5Lk5NJLQVRv8lebjepo,467
nibabel-5.3.2.dist-info/licenses/COPYING,sha256=WrBjg5IgPXN7TWxw45IKYYiO_e596EqDdPNiZSIwbcc,9830
nibabel/.gitignore,sha256=-sQk7_8D35QL6aXSJKzrsrS53YPwH7IjF--HauLUg64,208
nibabel/__init__.py,sha256=MPekuYWE06czrvBwixmBn5NCxfPwKI3cB2gLY7yi23M,4978
nibabel/__pycache__/__init__.cpython-310.pyc,,
nibabel/__pycache__/_compression.cpython-310.pyc,,
nibabel/__pycache__/_version.cpython-310.pyc,,
nibabel/__pycache__/affines.cpython-310.pyc,,
nibabel/__pycache__/analyze.cpython-310.pyc,,
nibabel/__pycache__/arrayproxy.cpython-310.pyc,,
nibabel/__pycache__/arraywriters.cpython-310.pyc,,
nibabel/__pycache__/batteryrunners.cpython-310.pyc,,
nibabel/__pycache__/brikhead.cpython-310.pyc,,
nibabel/__pycache__/caret.cpython-310.pyc,,
nibabel/__pycache__/casting.cpython-310.pyc,,
nibabel/__pycache__/conftest.cpython-310.pyc,,
nibabel/__pycache__/data.cpython-310.pyc,,
nibabel/__pycache__/dataobj_images.cpython-310.pyc,,
nibabel/__pycache__/deprecated.cpython-310.pyc,,
nibabel/__pycache__/deprecator.cpython-310.pyc,,
nibabel/__pycache__/dft.cpython-310.pyc,,
nibabel/__pycache__/ecat.cpython-310.pyc,,
nibabel/__pycache__/environment.cpython-310.pyc,,
nibabel/__pycache__/eulerangles.cpython-310.pyc,,
nibabel/__pycache__/filebasedimages.cpython-310.pyc,,
nibabel/__pycache__/fileholders.cpython-310.pyc,,
nibabel/__pycache__/filename_parser.cpython-310.pyc,,
nibabel/__pycache__/fileslice.cpython-310.pyc,,
nibabel/__pycache__/fileutils.cpython-310.pyc,,
nibabel/__pycache__/funcs.cpython-310.pyc,,
nibabel/__pycache__/imageclasses.cpython-310.pyc,,
nibabel/__pycache__/imageglobals.cpython-310.pyc,,
nibabel/__pycache__/imagestats.cpython-310.pyc,,
nibabel/__pycache__/info.cpython-310.pyc,,
nibabel/__pycache__/loadsave.cpython-310.pyc,,
nibabel/__pycache__/minc1.cpython-310.pyc,,
nibabel/__pycache__/minc2.cpython-310.pyc,,
nibabel/__pycache__/mriutils.cpython-310.pyc,,
nibabel/__pycache__/nifti1.cpython-310.pyc,,
nibabel/__pycache__/nifti2.cpython-310.pyc,,
nibabel/__pycache__/onetime.cpython-310.pyc,,
nibabel/__pycache__/openers.cpython-310.pyc,,
nibabel/__pycache__/optpkg.cpython-310.pyc,,
nibabel/__pycache__/orientations.cpython-310.pyc,,
nibabel/__pycache__/parrec.cpython-310.pyc,,
nibabel/__pycache__/pkg_info.cpython-310.pyc,,
nibabel/__pycache__/pointset.cpython-310.pyc,,
nibabel/__pycache__/processing.cpython-310.pyc,,
nibabel/__pycache__/pydicom_compat.cpython-310.pyc,,
nibabel/__pycache__/quaternions.cpython-310.pyc,,
nibabel/__pycache__/rstutils.cpython-310.pyc,,
nibabel/__pycache__/spaces.cpython-310.pyc,,
nibabel/__pycache__/spatialimages.cpython-310.pyc,,
nibabel/__pycache__/spm2analyze.cpython-310.pyc,,
nibabel/__pycache__/spm99analyze.cpython-310.pyc,,
nibabel/__pycache__/tmpdirs.cpython-310.pyc,,
nibabel/__pycache__/tripwire.cpython-310.pyc,,
nibabel/__pycache__/viewers.cpython-310.pyc,,
nibabel/__pycache__/volumeutils.cpython-310.pyc,,
nibabel/__pycache__/wrapstruct.cpython-310.pyc,,
nibabel/__pycache__/xmlutils.cpython-310.pyc,,
nibabel/_compression.py,sha256=FJjY3a8HJVBwTB90HGkt0H7Jle_2qoa8yXqeAbBFZKI,1486
nibabel/_version.py,sha256=s8xzXnWnLELN5xo2TAKS0quaihPhIsn6_jp46rRCtQE,160
nibabel/_version.pyi,sha256=_KrIYj3_s0sXcBhl_WIQrYZUP2PwYJRhD9HwaO1tnio,96
nibabel/affines.py,sha256=pWf1wfxAkPVkV97RJhcfh45fkyhycPfuawJG0fDbSKY,12814
nibabel/analyze.py,sha256=iCc-DhtSmIM8Uf2vSwLNuDn3f3kh-NAhhnRmp2PnbA4,38113
nibabel/arrayproxy.py,sha256=34rc1keJzgigxeUIvGwDxjvXIQ1lw1JA0RZa-nxZqs0,19664
nibabel/arraywriters.py,sha256=YpFIaVYVmifBhd-bmrfAcAFr-6ZqKGnlh3E3d_s-ydA,27970
nibabel/batteryrunners.py,sha256=dAW3r5H038JzDjDfiyPqpUzgulFOYkiJcWpWuSj9zhQ,9339
nibabel/benchmarks/__init__.py,sha256=LdefQDtJR44IxDdZz3kfaR1OyzLR45XLzQAtO8PxPuA,25
nibabel/benchmarks/__pycache__/__init__.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/bench_array_to_file.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/bench_arrayproxy_slicing.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/bench_fileslice.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/bench_finite_range.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/bench_load_save.cpython-310.pyc,,
nibabel/benchmarks/__pycache__/butils.cpython-310.pyc,,
nibabel/benchmarks/bench_array_to_file.py,sha256=4L9zdGzNpTo0d4r6jfA6CrTMQnVfKoOuTTiRyKgWZr4,1900
nibabel/benchmarks/bench_arrayproxy_slicing.py,sha256=txQ-HhYTxpIm0vrjLxRA9oBosAV3GZq31zt7SEYw-2g,5939
nibabel/benchmarks/bench_fileslice.py,sha256=w0nY6Q8F3RcwTu8s7FnATTyr7yXnpd_A4qMESQYzTpI,3203
nibabel/benchmarks/bench_finite_range.py,sha256=KxkBkUYFqKaSa7bXQIrpUCOoW0yRsZ-dONiuSoE7LKs,1235
nibabel/benchmarks/bench_load_save.py,sha256=UN8wbgdpl2F9Kc-Ozvjof6Mbulb7gSVlq6oPgOmlAno,2051
nibabel/benchmarks/butils.py,sha256=vCXfm8gTxOx7m5t2-BKGGYGqTjTMer8fBXIMpsrMJDM,268
nibabel/benchmarks/pytest.benchmark.ini,sha256=P33KK5wU94HlG8DqBE-e9DbV3iKK06QM4KwQc_zDlTQ,85
nibabel/brikhead.py,sha256=t0323PaOZWGdUIEZ2zJKRU4M1uQ7mkHKWjF2bbEqR7Y,19369
nibabel/caret.py,sha256=iCDSNcv8U_2FMLTUyOgBXV8itUML6uJ10o2VOEXBBLw,3422
nibabel/casting.py,sha256=YTO9sfmv6MYdluRgjCCxlI-C_nikokk7Hp9aRxKq4m8,25472
nibabel/cifti2/__init__.py,sha256=dW7PSs19ZovPN4w4ot9cTaOKWvXhIZXWCzuB5dZe4iA,1132
nibabel/cifti2/__pycache__/__init__.cpython-310.pyc,,
nibabel/cifti2/__pycache__/cifti2.cpython-310.pyc,,
nibabel/cifti2/__pycache__/cifti2_axes.cpython-310.pyc,,
nibabel/cifti2/__pycache__/parse_cifti2.cpython-310.pyc,,
nibabel/cifti2/cifti2.py,sha256=QGhev71UkbS391PJAM5Wx5q6S3Q50b9Xu7nexzqA1Tc,54414
nibabel/cifti2/cifti2_axes.py,sha256=tZdT1tssMq2z85N73h4YonTX1CwgiHlqzytLlhw6xZY,53879
nibabel/cifti2/parse_cifti2.py,sha256=DaWGrUpvHFBp7XhJUWjdAoWpfibfUF0nlkhH24oemPA,21388
nibabel/cifti2/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/cifti2/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_axes.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_cifti2.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_cifti2io_axes.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_cifti2io_header.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_name.cpython-310.pyc,,
nibabel/cifti2/tests/__pycache__/test_new_cifti2.cpython-310.pyc,,
nibabel/cifti2/tests/test_axes.py,sha256=Kn_WoU2Fw4x4l7TpysOcDDQy1tvWHoIk7QGjHyrl9iQ,22696
nibabel/cifti2/tests/test_cifti2.py,sha256=ny5YOMY5J2xOR5F4XQ1m2RNcEAaDWzjrgr_opAmu88g,12125
nibabel/cifti2/tests/test_cifti2io_axes.py,sha256=I9E7311fgyYSBZ3GZpAjVhltJCUS-17dZGenqF_1nJk,8652
nibabel/cifti2/tests/test_cifti2io_header.py,sha256=Ouf2zXvqviszVyOKNifwAnw_i0fkT0evvuDU8814QBw,20936
nibabel/cifti2/tests/test_name.py,sha256=FeW8gn9AD_IT0IOLnzngxQ5MnMrczrZk1-jaoG4MZbM,1126
nibabel/cifti2/tests/test_new_cifti2.py,sha256=CzODwPE6RmAnV69gtFj0bHQSljn3mKiIxTJPE0evRtk,19503
nibabel/cmdline/__init__.py,sha256=rlL7kV8xS5J_FiDPc1p8dvKMLP60kVhbIrCtzyJmr3E,440
nibabel/cmdline/__pycache__/__init__.cpython-310.pyc,,
nibabel/cmdline/__pycache__/conform.cpython-310.pyc,,
nibabel/cmdline/__pycache__/convert.cpython-310.pyc,,
nibabel/cmdline/__pycache__/dicomfs.cpython-310.pyc,,
nibabel/cmdline/__pycache__/diff.cpython-310.pyc,,
nibabel/cmdline/__pycache__/ls.cpython-310.pyc,,
nibabel/cmdline/__pycache__/nifti_dx.cpython-310.pyc,,
nibabel/cmdline/__pycache__/parrec2nii.cpython-310.pyc,,
nibabel/cmdline/__pycache__/roi.cpython-310.pyc,,
nibabel/cmdline/__pycache__/stats.cpython-310.pyc,,
nibabel/cmdline/__pycache__/tck2trk.cpython-310.pyc,,
nibabel/cmdline/__pycache__/trk2tck.cpython-310.pyc,,
nibabel/cmdline/__pycache__/utils.cpython-310.pyc,,
nibabel/cmdline/conform.py,sha256=M6djxBTKhsTYkC4_jVjGUkV4mEKjd5P3w6TyiKHyX0I,2020
nibabel/cmdline/convert.py,sha256=kNAycBc2tLVF6p3-heOM5oQ6IoMN4P4JfX1rHhIQ4lc,2233
nibabel/cmdline/dicomfs.py,sha256=E8S6TmUMKQJl9GkVxBgmDzafhWGQkebnoS4711tgfQc,7922
nibabel/cmdline/diff.py,sha256=emx_XRHoogYtHLCkUkV94i8kMBaBFgVtTuJBkgOXyxE,12261
nibabel/cmdline/ls.py,sha256=gk6BkvQVYjoDp9Yz1Zb2vAYe8ztHBJKtrJPYArmctNI,6114
nibabel/cmdline/nifti_dx.py,sha256=Iu3UuBmMsQNhoCpme1Au3kUgKJ69xJA46XG8lj0Nd4w,1659
nibabel/cmdline/parrec2nii.py,sha256=O61iIp9VubasUspHlWhy4UdQjGPmS9twQDwQaNWG3HE,15038
nibabel/cmdline/roi.py,sha256=tgvKX2ROaUVTktWcvsgreKfh48QpYQyUn2d446_w8rg,3233
nibabel/cmdline/stats.py,sha256=1BmJgwxwGojv1YweyO8AMgCaRZpF_1ODdff6EXZ2GlM,1600
nibabel/cmdline/tck2trk.py,sha256=X2IfY85X60TdI8Erk7fC61W4Ss6eOyf9QzfSp6mwI-E,1843
nibabel/cmdline/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/cmdline/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_conform.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_convert.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_parrec2nii.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_roi.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_stats.cpython-310.pyc,,
nibabel/cmdline/tests/__pycache__/test_utils.cpython-310.pyc,,
nibabel/cmdline/tests/test_conform.py,sha256=WByPL3iO8IWg6GcEP8HPNwwbM5tRZ1mvpBhQFcyAhFA,1863
nibabel/cmdline/tests/test_convert.py,sha256=nDSpJc0m1SMHC26Z43LbfPU-9I7MfFlSixzc3icgvts,5316
nibabel/cmdline/tests/test_parrec2nii.py,sha256=skS1xk8PKdnicDYn1M3mZ5WydIIxoSjhg0vh70MBVrc,3059
nibabel/cmdline/tests/test_roi.py,sha256=bp3TrgAmVcMsjwIyKcYK0DeCNptBRq-QoyCRvzpQD30,5459
nibabel/cmdline/tests/test_stats.py,sha256=SUjEOHSyOz19-zuOPeWZf0Ve-m6Y_sVTd-6QQBoxbHo,1010
nibabel/cmdline/tests/test_utils.py,sha256=8J9iZmHEZrJrlYKqTej1i_es49D95loKoWxRDQNXVZw,10721
nibabel/cmdline/trk2tck.py,sha256=ah9TUe67Gd_2mJ6mmwXBrkbpBohWVGEye7Wm8TEmYHw,1199
nibabel/cmdline/utils.py,sha256=dMT3B4H3u4frBkLMkaQFtSQgFzt3KFr8Iqjb784bmpU,3431
nibabel/conftest.py,sha256=D3OMLVOqegqxZshPz6WzOeDroRx7_iIOLMS8cdmyu98,827
nibabel/data.py,sha256=96AQO4sIgnP4dcKJkRaebcqNq8uD4sZhMjKU9C4I3uo,11609
nibabel/dataobj_images.py,sha256=lqRZZCsQjpq9kvJPfkvEf7PJCDgpBW2XrmIuSwxo0q4,21096
nibabel/deprecated.py,sha256=SJvuiE6stM89FDbIAnY47kRZU5fFjfSgCa2ulGxvspc,3512
nibabel/deprecator.py,sha256=8-eLmTNYC8CIT40Bvt2j3JFhsHOswvfW2DfHoeU6phc,7957
nibabel/dft.py,sha256=EXwdDopfhlC7kfGiSy-nkN6l85rychZFn4zqkoSEtTs,18267
nibabel/ecat.py,sha256=vY6y-Wox9EMHg4XABU9V_a2aAKlGAqRdb8YaB0Azqgo,35325
nibabel/environment.py,sha256=HdzoPNxmZdmnHG0xNZAlNTO1tQHTvS3RovfWMALiMZg,2162
nibabel/eulerangles.py,sha256=4hFy9XbF2jkerNYnuPX5H5cvFe7pqCIJVJeni4OVypw,13292
nibabel/externals/__init__.py,sha256=5l23GRqPdX6lNAwBkVVErn2JwOfPKcAb3Z398egtiLA,65
nibabel/externals/__pycache__/__init__.cpython-310.pyc,,
nibabel/externals/__pycache__/conftest.cpython-310.pyc,,
nibabel/externals/__pycache__/netcdf.cpython-310.pyc,,
nibabel/externals/__pycache__/oset.cpython-310.pyc,,
nibabel/externals/conftest.py,sha256=Gg8fDQRVzn5Yw-j4Muy0qgnPl-4gL0WvazsO69llKrU,539
nibabel/externals/netcdf.py,sha256=pq_Lv3D-bhhxDdOF4PhA8jdPM8-sJcvi8x6kSoOVIYg,38907
nibabel/externals/oset.py,sha256=0BhnQwpBXwoxwR7McPVUSq9GQG-o33M42CCC6KKLwbI,2434
nibabel/externals/tests/__init__.py,sha256=wjqwxrZFkuFMFp9lIfsMadUpKOcwy7fP4nmmp8XJ49E,33
nibabel/externals/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/externals/tests/__pycache__/test_netcdf.cpython-310.pyc,,
nibabel/externals/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
nibabel/externals/tests/test_netcdf.py,sha256=Yd2J8_5sfbrHgIF7wFQSpSKAH6h9xRmKvQ7-wONJytQ,5477
nibabel/filebasedimages.py,sha256=Tw0H_PXVeG6VjvmFHGRbDrbQ1LzQgvKF-G3W7GTfAUo,21459
nibabel/fileholders.py,sha256=7LBGKv1G34Hadb-oSkMaDLlLaQMgkHlTRAluePOf8ws,3632
nibabel/filename_parser.py,sha256=IPRybNI_YPdLNMVds9ieTz5kVTgoKgYNnRyxsp1zukM,10964
nibabel/fileslice.py,sha256=332KaCHUos8A7TtZNeXmft67AtjxgKZtnRek2uu3Q8s,30316
nibabel/fileutils.py,sha256=hOTuQczb1zGN1wO0CnoG-hYrzPLG4dlcfRI6VD0v2sc,2330
nibabel/freesurfer/__init__.py,sha256=-5RYO-TwD8llxFF2cSMsn_8QUTRXgAy9ujc9jfi95ss,241
nibabel/freesurfer/__pycache__/__init__.cpython-310.pyc,,
nibabel/freesurfer/__pycache__/io.cpython-310.pyc,,
nibabel/freesurfer/__pycache__/mghformat.cpython-310.pyc,,
nibabel/freesurfer/io.py,sha256=EwCRjZgBWQV-7z6w6HiMwSJn1rKtv42W1lBabxqssTY,20278
nibabel/freesurfer/mghformat.py,sha256=jInv-2P3bRAjXKK-5yngiaOLPZ9yUXUXZj3Vkq_7ibI,21185
nibabel/freesurfer/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/freesurfer/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/freesurfer/tests/__pycache__/test_io.cpython-310.pyc,,
nibabel/freesurfer/tests/__pycache__/test_mghformat.cpython-310.pyc,,
nibabel/freesurfer/tests/test_io.py,sha256=rX1qBJKM8MRoT6LsiD3mIm_7ITNL7cE5tWJfaZpo0lk,14571
nibabel/freesurfer/tests/test_mghformat.py,sha256=zuzrq-wb1EAsUgvjXP2OTc-OatJrUnVsJ8iUr7ywB2A,16254
nibabel/funcs.py,sha256=uIWQFZyeePefjycMgr3Hd1qpiv4aaZZj4syiAlCgMkA,7125
nibabel/gifti/__init__.py,sha256=pwIhVTsop93t8l40xjnfsoozk0qOO6G42XEpzUnudT4,653
nibabel/gifti/__pycache__/__init__.cpython-310.pyc,,
nibabel/gifti/__pycache__/gifti.cpython-310.pyc,,
nibabel/gifti/__pycache__/parse_gifti_fast.cpython-310.pyc,,
nibabel/gifti/__pycache__/util.cpython-310.pyc,,
nibabel/gifti/gifti.py,sha256=pCjl0OBnF_gxKVvvLzN_PlTaodeGD3X8FrgRB95XayE,33209
nibabel/gifti/parse_gifti_fast.py,sha256=koxXx10tocGDAX-bvjjXnCiVgka2HOBiAfcMYxAvYwY,14468
nibabel/gifti/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/gifti/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/gifti/tests/__pycache__/test_1.cpython-310.pyc,,
nibabel/gifti/tests/__pycache__/test_gifti.cpython-310.pyc,,
nibabel/gifti/tests/__pycache__/test_parse_gifti_fast.cpython-310.pyc,,
nibabel/gifti/tests/data/ascii.gii,sha256=IkQVuYgTQGWylnwkbGxP9V99TzqUQ3pctA5X4BGkGo8,2878
nibabel/gifti/tests/data/ascii_flat_data.gii,sha256=_tnnPc06oGKal4-LTEE4NHHeO6ZqsNu5JTIBl741cbk,3209
nibabel/gifti/tests/data/base64bin.gii,sha256=Y3dp81L0bsuyN9tv-2JxhW8mc6kGmxldY_7Wzlha90o,2049
nibabel/gifti/tests/data/external.dat,sha256=MRwdFr0UA7c3NJzm42-SV0dK0jd4cMyoacfZrvY8C9U,240
nibabel/gifti/tests/data/external.gii,sha256=2lyGPr-5_d6lqokmVTSEi3VVK6_IeIqssorkVXlNg0E,1498
nibabel/gifti/tests/data/external.gii.bz2,sha256=laqNpvR_hbysq0igFvwj5vod7L7GrFC3xi1puYwJD8U,558
nibabel/gifti/tests/data/external.gii.gz,sha256=yYNyUx5fpcSrrQtyqqfHzz2u5zNuA-HlYR4xItgUaJI,488
nibabel/gifti/tests/data/gzipbase64.gii,sha256=c9PKODszu1y_VolrvqpxxWADEX3ORXkrVG0RRKnST8k,712631
nibabel/gifti/tests/data/label.gii,sha256=5sRmYRnEqk5JvSECtMWTGHztF8PS5h8PDEi8waaflFg,862335
nibabel/gifti/tests/data/rh.aparc.annot.gii,sha256=ZoSzNDNB0db1c44fnr-F-WEgLTtWj4iwfnDHrGH-vp4,47942
nibabel/gifti/tests/data/rh.shape.curv.gii,sha256=-fVc_ikqTiMJYMplo25fYlplCF9LkAEn99srbwyBnlI,641433
nibabel/gifti/tests/data/task.func.gii,sha256=9W31thQnlBF-Njgppgn-IFGF3LYCb6CDoMeahnBm_W0,36622
nibabel/gifti/tests/test_1.py,sha256=Aki0Du6z-X-QH_vlk2yFMaQs1Aa_yvz13mr2wqncZXs,515
nibabel/gifti/tests/test_gifti.py,sha256=JnBxQG7J9oyMsq6WERIMmKBoETkMMEMFSJOVsmXrdEk,17684
nibabel/gifti/tests/test_parse_gifti_fast.py,sha256=dRjRQ_JavsOTWjHd4E2KrKCJErHYZbABeb3ILyx51s0,15109
nibabel/gifti/util.py,sha256=ngLLVrg2FS6hRXrP7uPOgwQN01Cif2ROTp_czsa2Cgs,1402
nibabel/imageclasses.py,sha256=URn9baG6ASp1psy9D63YL_0Q1aq8aUJEEWIrHFJxtj8,2321
nibabel/imageglobals.py,sha256=vHcs4dsaYnUqnC70G1VShYMJGEa51loUrl2eHHlVBGg,1990
nibabel/imagestats.py,sha256=bme2mWo6lkLvCR4XzAXmr6DXoMpAfcM0fR8zEyM--P4,1778
nibabel/info.py,sha256=dG6wF2sEd1lhvHCRFe8H0iDnIInMedFxctRzi2nMyOI,3962
nibabel/loadsave.py,sha256=S8k5tBR3qQqqMN_idr9C5lY1UdPUSd321-4urSWVtvs,10339
nibabel/minc1.py,sha256=5dSs8h21qHeNMwsCgrNRf6TrPmH_rLXD5ZE6Lw2-G3g,12649
nibabel/minc2.py,sha256=uRXiIDHXUWIPtW32t2o5WEDUTH4y5L-ZwHtBRAtOQCw,6516
nibabel/mriutils.py,sha256=zurJZ2RPbN2h3UPj6A-1k7-RUMXm6jodaeSx5jphoto,1534
nibabel/nicom/__init__.py,sha256=S8xelD5PrvyJxxbnQIcNuxELaeIpA-MEdZAATjt_f_k,843
nibabel/nicom/__pycache__/__init__.cpython-310.pyc,,
nibabel/nicom/__pycache__/ascconv.cpython-310.pyc,,
nibabel/nicom/__pycache__/csareader.cpython-310.pyc,,
nibabel/nicom/__pycache__/dicomreaders.cpython-310.pyc,,
nibabel/nicom/__pycache__/dicomwrappers.cpython-310.pyc,,
nibabel/nicom/__pycache__/dwiparams.cpython-310.pyc,,
nibabel/nicom/__pycache__/structreader.cpython-310.pyc,,
nibabel/nicom/__pycache__/utils.cpython-310.pyc,,
nibabel/nicom/ascconv.py,sha256=aAg0tluPzBs-jTszoyr6jJoVMzJZTk8Iyl3tLKPsKLE,6896
nibabel/nicom/csareader.py,sha256=0IuY05Cg1b7GhXCVjU9bZYLFbGlZBEW4vK8k9mows-0,7235
nibabel/nicom/dicomreaders.py,sha256=bErj1vG-VVPLaF-MZS9NaLrfUXMBnspQCvuCL4dLbYo,6531
nibabel/nicom/dicomwrappers.py,sha256=U-i5vvl0Yys4gbl7dgqfniPQuo7A6pCUiNuFBJ7d5Hs,48408
nibabel/nicom/dwiparams.py,sha256=RIKm9YgyWjZCWaDOlH0hKxQUO3Xy1sVpufk5kx2QhTg,4689
nibabel/nicom/structreader.py,sha256=Qdec24cBnhzq4HRMqCv5MHHy6nbyA8w47YwoQ8BN_p8,3566
nibabel/nicom/tests/__init__.py,sha256=rL5BHjFAW65z_pyxtUissQfGw73JR_Sj39ov34x99EM,189
nibabel/nicom/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/data_pkgs.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_ascconv.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_csareader.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_dicomreaders.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_dicomwrappers.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_dwiparams.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_structreader.cpython-310.pyc,,
nibabel/nicom/tests/__pycache__/test_utils.cpython-310.pyc,,
nibabel/nicom/tests/data/0.dcm,sha256=cEXfl_P4MA868vXvQAa3e4w8EYG1Zo1fmkeD0jdcbbs,226390
nibabel/nicom/tests/data/1.dcm,sha256=35DfehF0uxye_Luc6xUbigL_Dsxi-G-F7G0etAB2NIk,226390
nibabel/nicom/tests/data/ascconv_sample.txt,sha256=IFqdSDD_yz2JrQVNgSq8Cb_6hcl-nwkeYi0ocgds5n4,46970
nibabel/nicom/tests/data/csa2_b0.bin,sha256=iGMtom1f_ZBQGMYGSvrM_tTdd_snR4Itgkp1fk9BUW4,11560
nibabel/nicom/tests/data/csa2_b1000.bin,sha256=l3lVblb3YZOWkskmQcHt6KFLD8iunCzdCChnW3pyHNg,11864
nibabel/nicom/tests/data/csa2_zero_len.bin.gz,sha256=dWmSW428M8NunmebVOExkTILgOsT6hrR_5FoXrrsqB0,9568
nibabel/nicom/tests/data/csa_slice_norm.dcm,sha256=hn6vCeOa3tR95cGBRFtCYnyr7cA9KukiR5jRhZFJMew,13400
nibabel/nicom/tests/data/csa_str_1001n_items.bin,sha256=2_sLB1vYNKTFqmQluWK8iNSWEaLAPF_oT4yt5BK7Z7Y,16116
nibabel/nicom/tests/data/csa_str_valid.bin,sha256=gVLQcW7eQvSTQFvh1kaBzf_XNuMR9PPx70u3dNrGVE0,132
nibabel/nicom/tests/data/decimal_rescale.dcm,sha256=iBJ040gy4dK9m0Fp_o3EvmkzVlgkQifuXDJIpf25nxA,26650
nibabel/nicom/tests/data/philips_mprage.dcm.gz,sha256=WXg11Q6usdhCPC9iB1-scTNlKhzk6YEqLREY06uD67Y,41441
nibabel/nicom/tests/data/siemens_dwi_0.dcm.gz,sha256=TIgz-QOzKWYFFdNIz7PQPkbSzaE9wCaUbnQYhAmC5Io,19321
nibabel/nicom/tests/data/siemens_dwi_1000.dcm.gz,sha256=DVxa6h496a143fu9hcIg1GTsZuQc0M9neJ4fTMbzrKQ,19391
nibabel/nicom/tests/data/slicethickness_empty_string.dcm,sha256=Qmjusm1JQ4Os63XlAH_Et78mnGzKu01kCX_BjmFekFI,41726
nibabel/nicom/tests/data_pkgs.py,sha256=16Kw4ezj41olGTCQVHNVBkOWtG39T2DZl6YoBJvNfLw,369
nibabel/nicom/tests/test_ascconv.py,sha256=1bQiRo3HRx0VnOCULGL-v7VJpaRJM08iE9oGVYxvwyw,2623
nibabel/nicom/tests/test_csareader.py,sha256=Xm1NRdlaOK2hLkwRKXasQmLAUW3CgXCIUsmQ2kueZo4,4649
nibabel/nicom/tests/test_dicomreaders.py,sha256=TfEDVP20jszWQ0nm4gfOygtFfrdlS4zGg87Kw4RX0bg,2274
nibabel/nicom/tests/test_dicomwrappers.py,sha256=cUbQlmbiXmtajwoberi2lw9xZzInpt0xB1Yr3lXVBfI,47728
nibabel/nicom/tests/test_dwiparams.py,sha256=1rejsgAEEe_lgPeHaFo06n47uKmXtTXcytyqpUr5jR4,1651
nibabel/nicom/tests/test_structreader.py,sha256=1gMLT4skyHh6lK64oZHLxPMULZQ9drfDVpgbRAw5sco,1427
nibabel/nicom/tests/test_utils.py,sha256=rX6rjkwxF60pGgfrzWC7fehzexbnEnCNqaQfLk1jMWI,2867
nibabel/nicom/utils.py,sha256=GlO4brEerxnHdQjdmwheUO4C56-A95M_CnYK479416Y,3331
nibabel/nifti1.py,sha256=HkePlZLM2vuiN_9NZzOt8mHXfKfFZDwInTSYIAwVR7Y,95443
nibabel/nifti2.py,sha256=tLoa7pOYXee3kYjjkYqQ_HDDGXsfTtY5EbeinuwzCEg,10858
nibabel/onetime.py,sha256=tF51yCG_dw8y8lwgntSauVug3NwxaOq0KmeAEbioHHs,4668
nibabel/openers.py,sha256=pyA9koiaJo2EL_iI5ZVc13Ke2KUSUs3W29k6Y6wr4OQ,9540
nibabel/optpkg.py,sha256=l7c6MfE1dFH_n4o6wlQu5QLx46DAWyjInxgSdd7MQzY,4402
nibabel/orientations.py,sha256=CHPphM-pRslZm21crH7P5h4ebOPJLaGdUlZ-IWTD4lE,14026
nibabel/parrec.py,sha256=7iZ4_0JcLKX_kBaZDS3LY5WObUDGQvAdAryfk7inwbw,51862
nibabel/pkg_info.py,sha256=elCi7duAoAFFkfmgN5ab8XMGcgQhF52PIZ65USYnbBg,4212
nibabel/pointset.py,sha256=jPRDjyXDW-JyBQNFr0OlfHogQS_Fvmn7eaGeOk9xI-U,6605
nibabel/processing.py,sha256=5kOI924CfSbiyhmOGy9QzJAoT2Es9X_I6LBg-d9AfSE,14669
nibabel/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/pydicom_compat.py,sha256=AqCvQO9q5UjepEn1BW6AmeHurh1UaupWuAih2RDXKKc,1880
nibabel/quaternions.py,sha256=qDXBETcXY_pXou7LMQ2pTm0fh0UF61cRMK715b1d48c,13694
nibabel/rstutils.py,sha256=U-v2DZl-aPhNrRCD1giSbit6vzOlnX6EL8hXHK7t-gY,4559
nibabel/spaces.py,sha256=ZkhnCIrRSxPYYrUsebXRQzP_cIUV_EDSAUQv3SxEV-s,5436
nibabel/spatialimages.py,sha256=YOOZ66Mqw7hVn-NEvgpJR3xbGcRfECbej3rjWitgc3M,23465
nibabel/spm2analyze.py,sha256=AGAMvQwNJj7ukTrDH3qpvUIfZOlTBhkTW9H4Jt5XSYI,4701
nibabel/spm99analyze.py,sha256=55ip_e0E8eKFSZsg5X08SebN_T5opyeKcV9zzUDNsw8,12496
nibabel/streamlines/__init__.py,sha256=-GnKOTOMBizCDCKUt7UuFpx8HUYcghp0C-iCzDiphzo,4435
nibabel/streamlines/__pycache__/__init__.cpython-310.pyc,,
nibabel/streamlines/__pycache__/array_sequence.cpython-310.pyc,,
nibabel/streamlines/__pycache__/header.cpython-310.pyc,,
nibabel/streamlines/__pycache__/tck.cpython-310.pyc,,
nibabel/streamlines/__pycache__/tractogram.cpython-310.pyc,,
nibabel/streamlines/__pycache__/tractogram_file.cpython-310.pyc,,
nibabel/streamlines/__pycache__/trk.cpython-310.pyc,,
nibabel/streamlines/__pycache__/utils.cpython-310.pyc,,
nibabel/streamlines/array_sequence.py,sha256=CpxiPKbRz1dQvIZkee66HBeJGlBMP-_I5Ue8yjfKMRE,22473
nibabel/streamlines/header.py,sha256=upWdHYFytgUY1PvbCn3PojvME6rcXOgVdlzttWNKdfo,672
nibabel/streamlines/tck.py,sha256=APcce7P4OcMLVLnWlWVzH5LnyMr2zAz36k0tIUSuL_E,17944
nibabel/streamlines/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/streamlines/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_array_sequence.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_streamlines.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_tck.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_tractogram.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_tractogram_file.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_trk.cpython-310.pyc,,
nibabel/streamlines/tests/__pycache__/test_utils.cpython-310.pyc,,
nibabel/streamlines/tests/test_array_sequence.py,sha256=FQ9EJ09BTX5ZzxFtIa5-bViTc-U0PmnFxGHjND6wqJo,19541
nibabel/streamlines/tests/test_streamlines.py,sha256=gHXDBk5cjsiX5D7mqcwcDZ59N5X73-eflJHrNLh1_Hs,11246
nibabel/streamlines/tests/test_tck.py,sha256=LgyYpJUglSAgreysJAbOWXsXPjJCQlw7Gt6MrQB_-D0,11164
nibabel/streamlines/tests/test_tractogram.py,sha256=w6w8S6tCXnbYt_VL_yHuHeTb82sz9tvgKNDxRfBJtIU,45056
nibabel/streamlines/tests/test_tractogram_file.py,sha256=a7_eK5aT7OV5wqVfjaNZncVKTTX_2Q71GMlSeVAczA4,2131
nibabel/streamlines/tests/test_trk.py,sha256=tI6t0Oo9W9BZI5AgDHQkXWcZfLB5dJB2ikyQztRNbJ8,21159
nibabel/streamlines/tests/test_utils.py,sha256=0GzKH2YKuOVQ7SVJM1a4rJNWjFom42m6SdNNJ1vPS6I,824
nibabel/streamlines/tractogram.py,sha256=s_tpN40qAFqYUboCUXaqNAuf0i7zRLnS23nHXpfnqmU,32919
nibabel/streamlines/tractogram_file.py,sha256=NvUOK9zlLbG6Qk2fnmLngNj1UsVtrY8IP1LTFmJuMu4,3383
nibabel/streamlines/trk.py,sha256=bdCqQpYySsrX1V94sCuIM66kRfG4fmWvsCpnI4S32vA,30058
nibabel/streamlines/utils.py,sha256=4fblRHaVJDCHJl_mjJM80emmxmwh3RhSP1sHlKlkGGk,1390
nibabel/testing/__init__.py,sha256=AFXdxzEoUfyP08N0xGukJjoUGCBfLySVyIAo1hYysAM,7787
nibabel/testing/__pycache__/__init__.cpython-310.pyc,,
nibabel/testing/__pycache__/helpers.cpython-310.pyc,,
nibabel/testing/__pycache__/np_features.cpython-310.pyc,,
nibabel/testing/helpers.py,sha256=G4TdS4qBOlvEnva49CQ2zYMNHTy7X-_xXE3AD68PDWI,1569
nibabel/testing/np_features.py,sha256=bvsJ2PywVFdJq9G2ex_qz4zrW7pmLRHMZ7Z0CyfZO6w,449
nibabel/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nibabel/tests/__pycache__/__init__.cpython-310.pyc,,
nibabel/tests/__pycache__/conftest.cpython-310.pyc,,
nibabel/tests/__pycache__/nibabel_data.cpython-310.pyc,,
nibabel/tests/__pycache__/scriptrunner.cpython-310.pyc,,
nibabel/tests/__pycache__/test_affines.cpython-310.pyc,,
nibabel/tests/__pycache__/test_analyze.cpython-310.pyc,,
nibabel/tests/__pycache__/test_api_validators.cpython-310.pyc,,
nibabel/tests/__pycache__/test_arrayproxy.cpython-310.pyc,,
nibabel/tests/__pycache__/test_arraywriters.cpython-310.pyc,,
nibabel/tests/__pycache__/test_batteryrunners.cpython-310.pyc,,
nibabel/tests/__pycache__/test_brikhead.cpython-310.pyc,,
nibabel/tests/__pycache__/test_casting.cpython-310.pyc,,
nibabel/tests/__pycache__/test_data.cpython-310.pyc,,
nibabel/tests/__pycache__/test_dataobj_images.cpython-310.pyc,,
nibabel/tests/__pycache__/test_deprecated.cpython-310.pyc,,
nibabel/tests/__pycache__/test_deprecator.cpython-310.pyc,,
nibabel/tests/__pycache__/test_dft.cpython-310.pyc,,
nibabel/tests/__pycache__/test_diff.cpython-310.pyc,,
nibabel/tests/__pycache__/test_ecat.cpython-310.pyc,,
nibabel/tests/__pycache__/test_ecat_data.cpython-310.pyc,,
nibabel/tests/__pycache__/test_endiancodes.cpython-310.pyc,,
nibabel/tests/__pycache__/test_environment.cpython-310.pyc,,
nibabel/tests/__pycache__/test_euler.cpython-310.pyc,,
nibabel/tests/__pycache__/test_filebasedimages.cpython-310.pyc,,
nibabel/tests/__pycache__/test_filehandles.cpython-310.pyc,,
nibabel/tests/__pycache__/test_fileholders.cpython-310.pyc,,
nibabel/tests/__pycache__/test_filename_parser.cpython-310.pyc,,
nibabel/tests/__pycache__/test_files_interface.cpython-310.pyc,,
nibabel/tests/__pycache__/test_fileslice.cpython-310.pyc,,
nibabel/tests/__pycache__/test_fileutils.cpython-310.pyc,,
nibabel/tests/__pycache__/test_floating.cpython-310.pyc,,
nibabel/tests/__pycache__/test_funcs.cpython-310.pyc,,
nibabel/tests/__pycache__/test_image_api.cpython-310.pyc,,
nibabel/tests/__pycache__/test_image_load_save.cpython-310.pyc,,
nibabel/tests/__pycache__/test_image_types.cpython-310.pyc,,
nibabel/tests/__pycache__/test_imageclasses.cpython-310.pyc,,
nibabel/tests/__pycache__/test_imageglobals.cpython-310.pyc,,
nibabel/tests/__pycache__/test_imagestats.cpython-310.pyc,,
nibabel/tests/__pycache__/test_init.cpython-310.pyc,,
nibabel/tests/__pycache__/test_loadsave.cpython-310.pyc,,
nibabel/tests/__pycache__/test_minc1.cpython-310.pyc,,
nibabel/tests/__pycache__/test_minc2.cpython-310.pyc,,
nibabel/tests/__pycache__/test_minc2_data.cpython-310.pyc,,
nibabel/tests/__pycache__/test_mriutils.cpython-310.pyc,,
nibabel/tests/__pycache__/test_nibabel_data.cpython-310.pyc,,
nibabel/tests/__pycache__/test_nifti1.cpython-310.pyc,,
nibabel/tests/__pycache__/test_nifti2.cpython-310.pyc,,
nibabel/tests/__pycache__/test_onetime.cpython-310.pyc,,
nibabel/tests/__pycache__/test_openers.cpython-310.pyc,,
nibabel/tests/__pycache__/test_optpkg.cpython-310.pyc,,
nibabel/tests/__pycache__/test_orientations.cpython-310.pyc,,
nibabel/tests/__pycache__/test_parrec.cpython-310.pyc,,
nibabel/tests/__pycache__/test_parrec_data.cpython-310.pyc,,
nibabel/tests/__pycache__/test_pkg_info.cpython-310.pyc,,
nibabel/tests/__pycache__/test_pointset.cpython-310.pyc,,
nibabel/tests/__pycache__/test_processing.cpython-310.pyc,,
nibabel/tests/__pycache__/test_proxy_api.cpython-310.pyc,,
nibabel/tests/__pycache__/test_quaternions.cpython-310.pyc,,
nibabel/tests/__pycache__/test_recoder.cpython-310.pyc,,
nibabel/tests/__pycache__/test_removalschedule.cpython-310.pyc,,
nibabel/tests/__pycache__/test_round_trip.cpython-310.pyc,,
nibabel/tests/__pycache__/test_rstutils.cpython-310.pyc,,
nibabel/tests/__pycache__/test_scaling.cpython-310.pyc,,
nibabel/tests/__pycache__/test_scripts.cpython-310.pyc,,
nibabel/tests/__pycache__/test_spaces.cpython-310.pyc,,
nibabel/tests/__pycache__/test_spatialimages.cpython-310.pyc,,
nibabel/tests/__pycache__/test_spm2analyze.cpython-310.pyc,,
nibabel/tests/__pycache__/test_spm99analyze.cpython-310.pyc,,
nibabel/tests/__pycache__/test_testing.cpython-310.pyc,,
nibabel/tests/__pycache__/test_tmpdirs.cpython-310.pyc,,
nibabel/tests/__pycache__/test_tripwire.cpython-310.pyc,,
nibabel/tests/__pycache__/test_viewers.cpython-310.pyc,,
nibabel/tests/__pycache__/test_volumeutils.cpython-310.pyc,,
nibabel/tests/__pycache__/test_wrapstruct.cpython-310.pyc,,
nibabel/tests/conftest.py,sha256=-vWM90iysAWdcyGEeb1-uwnorUBhofBSbbTOI3noTX0,672
nibabel/tests/data/.gitignore,sha256=4ITpkLxxjoesDNBPleJwYCPBevk3pblwsValAGKT--c,40
nibabel/tests/data/0.dcm,sha256=cEXfl_P4MA868vXvQAa3e4w8EYG1Zo1fmkeD0jdcbbs,226390
nibabel/tests/data/1.dcm,sha256=35DfehF0uxye_Luc6xUbigL_Dsxi-G-F7G0etAB2NIk,226390
nibabel/tests/data/ADC_Map.PAR,sha256=FYHdA2xMQtbe6tg9ywS_qv-8Caa2ZIvineddQd-u9fY,11896
nibabel/tests/data/ASL_3D_Multiecho.PAR,sha256=IJrBsssWaegzuF0q-W4frFArLcHbKhDJGeKzA-lD2vI,33533
nibabel/tests/data/DTI.PAR,sha256=Gp1vTj0HWv59j6ni6HL9XLxQZENJjOTGcEAtXXOt9sQ,28888
nibabel/tests/data/DTIv40.PAR,sha256=5S6tUysCLOmSblRtfDZkzZPCBK4nemVwHnvb05l6Fcs,24840
nibabel/tests/data/NA.PAR,sha256=XxUztkw7CION5hmNIuWzrXap8XK5HMSz0TYuDenJJLI,8091
nibabel/tests/data/Phantom_EPI_3mm_cor_20APtrans_15RLrot_SENSE_15_1.PAR,sha256=WlETPtejHoc1WnH-mEclnYo-kp0fLBWXDOTuSL1IJTk,17280
nibabel/tests/data/Phantom_EPI_3mm_cor_SENSE_8_1.PAR,sha256=0nQ_9zXBSqugEG1MVNc9zesxWXrvXtc1CABwiAswMZY,17230
nibabel/tests/data/Phantom_EPI_3mm_sag_15AP_SENSE_13_1.PAR,sha256=c6cV_ZSunSyFMdrSa7VCw7q810GPHsHJtKE5PURDkAc,17264
nibabel/tests/data/Phantom_EPI_3mm_sag_15FH_SENSE_12_1.PAR,sha256=EQa02rMtQKp3vEfnmi_fysh_nLvVbsq1pE3gPLinJ4o,17203
nibabel/tests/data/Phantom_EPI_3mm_sag_15RL_SENSE_11_1.PAR,sha256=tONqC4XVmqkrKyLZCGDE1lWQulc0ZbIBpHu-42gHTCM,17263
nibabel/tests/data/Phantom_EPI_3mm_sag_SENSE_7_1.PAR,sha256=FLvYUp5zRVMzCr4s4WRLxgkYx9eFCJJG6_P-76VbkAI,17252
nibabel/tests/data/Phantom_EPI_3mm_tra_-30AP_10RL_20FH_SENSE_14_1.PAR,sha256=_wXmiMTBL-cmwXoeyiQGZOk6qoXTkilj5seXQNzluO0,17290
nibabel/tests/data/Phantom_EPI_3mm_tra_15FH_SENSE_9_1.PAR,sha256=G3G5jmZWv2Gp3Mnn8GEQBSCpK1ZNHgLyVEQ7CuXAjX8,17201
nibabel/tests/data/Phantom_EPI_3mm_tra_15RL_SENSE_10_1.PAR,sha256=RCOmAsMwsexveSDFxcBmaqUZoDaR4MdIOL3He3j1h7c,17264
nibabel/tests/data/Phantom_EPI_3mm_tra_SENSE_6_1.PAR,sha256=FXCZqDo9WOM1F8HiKg-O8ZUbS8Bjz-lFUovdfLlRAkA,17252
nibabel/tests/data/README.rst,sha256=lxRVH2XL1LUNxzu0cv84Cv5ALsgEA1KsAbksArAbvLk,345
nibabel/tests/data/T1.PAR,sha256=qTX_bFw8FD1iVY_hgQITfEf58OPqVTBbGp81MOoL-vg,8365
nibabel/tests/data/T1_3echo_mag_real_imag_phase.PAR,sha256=Etmj58SRd6WINu5lXiI_8OsXwOebUZwzG6jaNyw3Mpc,110896
nibabel/tests/data/T1_dual_echo.PAR,sha256=o_6keDAm_tCN4IBfnwSjwJs5BgvzPxQgy2N_vg2SDDA,110888
nibabel/tests/data/T2-interleaved.PAR,sha256=c40f7Ekk5y0bmcbNFmbr2d4G6HBTEM3oSvdiY58nOco,8379
nibabel/tests/data/T2.PAR,sha256=zIKGbLoQYVFyYkJjppou4NCU3sN6MK2uDh7iSsBgioM,8367
nibabel/tests/data/T2_-interleaved.PAR,sha256=Ou7lFwWuwZhMT93GgFoeVzWxv_6MwYNyVHS4aD-u0VQ,11314
nibabel/tests/data/T2_.PAR,sha256=2Lp70Eii-ceRXladP3NdowUSlc4nuQLsGX4tu04gFOE,11300
nibabel/tests/data/__pycache__/check_parrec_reslice.cpython-310.pyc,,
nibabel/tests/data/__pycache__/gen_standard.cpython-310.pyc,,
nibabel/tests/data/__pycache__/make_moved_anat.cpython-310.pyc,,
nibabel/tests/data/analyze.hdr,sha256=5PBp_aH3MJFgzHTKdoNsOUcj8VKmJXT_jvecM2U5ozE,348
nibabel/tests/data/anatomical.nii,sha256=HAifN7ZZeji7QVeh4bP38T8bydTnqM_fr5HYXNj2ZZQ,68002
nibabel/tests/data/bad_attribute+orig.HEAD,sha256=N80Sh2ZwASOJ3UK9Gwz96kD8JHYH13meGQ9dd_hyCkY,2673
nibabel/tests/data/bad_datatype+orig.HEAD,sha256=2UYRTCfbs6xJ3shEUwOtCMFBmFTzdn7LWYYhnGpg0d4,2672
nibabel/tests/data/check_parrec_reslice.py,sha256=9Oz7EEWunT0RCsJRv9qhIBPPTHhYjNV62vj_qWG5yuM,2737
nibabel/tests/data/complex.trk,sha256=hxUGB0uFT6My-7s3UsjOxBX1lx0I9VCVdeHcc85JZ4s,1296
nibabel/tests/data/complex_big_endian.trk,sha256=OeQo27s_-Xxy2AyjRPLgF4qMf6db9IxfDQ11RrD7BM4,1296
nibabel/tests/data/empty.tck,sha256=2Ddg3Y_u8q4wDVJfZJCkK3q19aJ8vNxBojXNLohqZ6U,79
nibabel/tests/data/empty.trk,sha256=R_aQIx0RxGnB9egW3EJM9hIG4oi0PhHpdFdllzipTok,1000
nibabel/tests/data/example4d+orig.BRIK.gz,sha256=du6QbU558kIKvCYnBw5Zm_NPwx8WA4qKsLqugrMcY8k,177051
nibabel/tests/data/example4d+orig.HEAD,sha256=nBKlMqmAvvVHm83GOw85jSUKgAEsOuM6wUq8uaDYpXY,2672
nibabel/tests/data/example4d.nii.gz,sha256=Qgl9-6udKgNrQa5cl6NZWRzyz1w_jcbKZFXAuKfyJpY,346451
nibabel/tests/data/example_nifti2.nii.gz,sha256=pT5Z5w6w2CdaT-NHQiqJVRruktDrETejREsZMqKMP-I,21160
nibabel/tests/data/fieldmap.PAR,sha256=8Eebe1BrflC-5MtllVSHeKey0UjHHXKc2S1WnCBoW_8,11309
nibabel/tests/data/functional.nii,sha256=BZHZ-MIfGgr0ZWfEf5Yweuj69rcHcaiB9MxHdQKveyY,43192
nibabel/tests/data/gen_standard.py,sha256=rRXIxjy77tgt_gKovPZ8SZfV-lKS_8b5F35yOHyphtc,2457
nibabel/tests/data/make_moved_anat.py,sha256=SHF_bOnwGMGOYgQGghoGN2LUYLMsjSWwp09-pEJFEK0,687
nibabel/tests/data/matlab_nan.tck,sha256=uF9w8ppBXLJfynwCc_-B3BgG6IFEbP7fwIXMDnNkzzA,1657
nibabel/tests/data/minc1-no-att.mnc,sha256=l6pi_y5J4x6o0n2vDpwQMOXEG5Fqp0HozfAT4HAToCE,6872
nibabel/tests/data/minc1_1_scale.mnc,sha256=GPhaihensG4z7_3A8dUkHSJL8MeowWBGX_k_DJrIeuo,6836
nibabel/tests/data/minc1_4d.mnc,sha256=T8kKpaUSdKmf_sSS4b3d6lyEdDEj1DRRJkFdeczB2lU,11804
nibabel/tests/data/minc2-4d-d.mnc,sha256=K31AmQR2Gt2L6JKFsyiFmilfAxJmzMHeGw1Xnwz9QSs,183809
nibabel/tests/data/minc2-no-att.mnc,sha256=dWqzrdVk3csBtPKp3RnP0jsZnXgbp58UbdnAv9jKyHw,21659
nibabel/tests/data/minc2_1_scale.mnc,sha256=-JEMPLmm-y5LsKm__xIZhhEJEZvAAlJcdiDJOxVl5J4,18268
nibabel/tests/data/minc2_4d.mnc,sha256=qUJCCUXWbpinL9C8Lkpb_Stc72xfnJrSht7xOoo14xU,27738
nibabel/tests/data/minc2_baddim.mnc,sha256=g8WhsjuGkVSjx9RSZo-EjimZrV9CfxKmi2kGg4ZoDGQ,19825
nibabel/tests/data/multiline_header_field.tck,sha256=jFe8B1fGHieMDd2JBuG5Jqj6_7sKcSR-Yky0INfRBiI,4411
nibabel/tests/data/nifti1.hdr,sha256=NWQ1-wa2fWpipDdWFCQoJoOrFGEZI6Ljhikl2Jrj2BY,352
nibabel/tests/data/nifti2.hdr,sha256=7DFN8k_7BuFzVROYDktYraVARagou4u-iQqfZ1AbhJE,544
nibabel/tests/data/no_header_end.tck,sha256=-M2Cnevz0ICA9wP-KlY9W29JI8lhMH2cxfCrGYKNef4,81
nibabel/tests/data/no_header_end_eof.tck,sha256=-BUO31jd6cz-H03BL0RyB0F98eEypcsyvrtz1FXibFo,62
nibabel/tests/data/no_magic_number.tck,sha256=jp_cUMJ2w0xvTrCnrtOxYdA_M0UxGfRHWQzX_4G3bbU,71
nibabel/tests/data/phantom_EPI_asc_CLEAR_2_1.PAR,sha256=9x6k_D3Hoi7xzxllwD13UMqRfEwlysq7yMP7ZUctc_g,13368
nibabel/tests/data/phantom_EPI_asc_CLEAR_2_1.REC,sha256=aifLbXGe2T9c4t-UfG-Nv6ul1FjPx_yYUJnb5UJLKqw,221184
nibabel/tests/data/phantom_fake_dualTR.PAR,sha256=Syjpjjf2TIZXisT7uoEqvnsBBGW2jazc2YaQgg525wU,13180
nibabel/tests/data/phantom_fake_v4.PAR,sha256=Vsj63OQLNs_SIW4sIxHfp_RQH3F3ctdsbrgNOwdmwQI,11609
nibabel/tests/data/phantom_fake_v4_1.PAR,sha256=33i13f068GBN_uYVsNNPgoyLDPEOZZF2ZzmcauSsJ5g,13174
nibabel/tests/data/phantom_truncated.PAR,sha256=fiqZly6Se1_wpecGVMiFg45lYsKccMcFtNzAxGhJ0-o,13368
nibabel/tests/data/phantom_truncated.REC,sha256=aifLbXGe2T9c4t-UfG-Nv6ul1FjPx_yYUJnb5UJLKqw,221184
nibabel/tests/data/phantom_varscale.PAR,sha256=_u217UVM64O3lFQATnBZ_sQ8O-Hxa4Wo58uPTMiaI4Q,13368
nibabel/tests/data/phantom_varscale.REC,sha256=aifLbXGe2T9c4t-UfG-Nv6ul1FjPx_yYUJnb5UJLKqw,221184
nibabel/tests/data/reoriented_anat_moved.nii,sha256=_VTPDOe1KTXtY-AkkKB8T12UmrJXLRPSYmABruyrF88,48400
nibabel/tests/data/resample_using_spm.m,sha256=xMSyGuCDOTE90ol3VawSXCRZ79eR8X9WvToqhZQDv8U,604
nibabel/tests/data/resampled_anat_moved.nii,sha256=GECgAioxbirKyrPhjnFqFaFA8gV_-It3cKCrP53THMM,4636
nibabel/tests/data/row_major.dconn.nii,sha256=REfPcWK7rVim2-iUpS-fRwwr7UFJshTys4x19PCP620,1888
nibabel/tests/data/scaled+tlrc.BRIK,sha256=UNIRxyX89L1qQXV8yvaXXdOo4yvdVuCWEyaanN7rIzs,218268
nibabel/tests/data/scaled+tlrc.HEAD,sha256=h4t_JgJxwUbbWExXBvogD2vv6_wOyUVhP3vGDiz5pdY,1923
nibabel/tests/data/simple.tck,sha256=P7-pBx6cg7zdEHawZNsJdKvd_6zNodz2EV66jIbGMZY,211
nibabel/tests/data/simple.trk,sha256=HTbrDNqa-y6s7hbU82qi8-fYsE_F5fxdTYpshTujUao,1108
nibabel/tests/data/simple_big_endian.tck,sha256=Ap1FaCBKWbZqDoqfOi3R4QsD_A23OVeOXeLAmpRE9z0,211
nibabel/tests/data/small.mnc,sha256=k9BM-3BUFR7izPNVCNOSDoPiy_hbCGvbnZcQkAgBNc4,40208
nibabel/tests/data/standard.LPS.trk,sha256=GPtme-tLM_DkeITtOasoiX6w5Gx5mcbMrE13d7-JA1s,5800
nibabel/tests/data/standard.nii.gz,sha256=-VVhBmaOeLWj71ag57yqBqnWpPmlqrDNVUjhmAlaC70,143
nibabel/tests/data/standard.tck,sha256=kmv9eN_50buSRJAc-bN80Ckz8Q9SmLKKgee0VCR2jC8,5839
nibabel/tests/data/standard.trk,sha256=XCzDudPF8PSZObO7FSQrafyRsZjxFYEX4z2p1wFA60c,5800
nibabel/tests/data/test.mgz,sha256=7g0JAi-jDcoMFpWadpiTFBEi_owEsZcI4iZFdtK3puk,680
nibabel/tests/data/tiny.mnc,sha256=Y4951MCczslIXmjpwWr0C3VKtgb17eMn3p9r5SpKDLw,7372
nibabel/tests/data/tinypet.v,sha256=GHn2xVXdd_UOcrabKYTd8Rrz_k4hVrzQ8QRGZX6cHh8,2136
nibabel/tests/data/to_canonical.m,sha256=bgA838Ekj-YCxPsEQWHR7rDArY6SPdqAy6WCDJDpOoM,2098
nibabel/tests/data/umass_anonymized.PAR,sha256=Duzt0RRwSSxkeIB38EXV7YZF-Hbmrgoho0mWfd0sl7M,26094
nibabel/tests/data/variant_v4_2_header.PAR,sha256=8XSb0cbH6jx_mk7Sse5B7z4IXklvZmL86AIlSrqcCYU,13303
nibabel/tests/nibabel_data.py,sha256=x3T1vHAfhT7KSc9_lqVXn9bR_2h3gSxvMJjm-TeL0oc,1626
nibabel/tests/scriptrunner.py,sha256=TmYaSA7kA8sAfggbtABjcIWEpQ3E-vvNsfu4B_KcrnQ,5718
nibabel/tests/test_affines.py,sha256=DuNSXjyylPDrK55qK53hmOSC44B3GoYB5inu2NquaHA,8327
nibabel/tests/test_analyze.py,sha256=nT6Pa3C7CtDFTkfsuykav3q_kpB4TcYzsfEAdxBadU4,35234
nibabel/tests/test_api_validators.py,sha256=Yf2y6Fklo219BmHCxgfUlfrAcD-db9dytGJefg_z-wc,3601
nibabel/tests/test_arrayproxy.py,sha256=PLuc6b5R10dukFxh-MomB2DOwprJg7NKqolgkjsBS3c,22049
nibabel/tests/test_arraywriters.py,sha256=AKe5tzPTofGaysEy_BSnUcyI86qzit89gc_tGur3Aoo,35610
nibabel/tests/test_batteryrunners.py,sha256=p74pwBRPVHE34eVKCdlQMle8RWu_iM1dySrxiGuv9l4,5449
nibabel/tests/test_brikhead.py,sha256=bC3ugPRlQhZSGRU7_GoZyCVcoDyOkFFwPfpLleBWEQY,4877
nibabel/tests/test_casting.py,sha256=OEVXqLKm8geUiKbN_Xo_JzlxI37JGr3614iZxbk9BWM,10493
nibabel/tests/test_data.py,sha256=fn1tS8zBWGVvjkvvWeBooL19f2C6a-EgDYN5enSTGzQ,8382
nibabel/tests/test_dataobj_images.py,sha256=YpaD_QMT7F5-ofnapFa3tVzIGwOJ4L6bK3KGdKa8EiA,1524
nibabel/tests/test_deprecated.py,sha256=BAfbIb-dPUrh63r8-Nkq89LXxHLvhBkBjPJLZfBDmkQ,3300
nibabel/tests/test_deprecator.py,sha256=2begm7NaHrNc3bcwef0_UKOQ7JSL5MDQCW4fYxN1rTU,5567
nibabel/tests/test_dft.py,sha256=ZOuYjfyoG8VqQtAbM5F3PAR1tC_1H_c_P7pf5I_A9a8,3761
nibabel/tests/test_diff.py,sha256=_DCAmhnTGUkyuR6_dBTf8Ryw9zSmYm1uFWu3RJuejFI,3273
nibabel/tests/test_ecat.py,sha256=4BToFWfcovE9pN6T23lQGPWnrnWUrVhym0k0Fm7nRHY,10113
nibabel/tests/test_ecat_data.py,sha256=tQR3q6xukpzQkiLsbOzkUboIYYC7Rrj6Ec8XYLEXhbw,2058
nibabel/tests/test_endiancodes.py,sha256=haqUXQe8n84s1gW_DRA3YZMtKSch6ep5hWtdFri2Hoo,1238
nibabel/tests/test_environment.py,sha256=3tXvFK5llOan0Q8kqBBvTjhFjRDzfN3biB_w-R6qo80,1754
nibabel/tests/test_euler.py,sha256=CFhI5sKtzkJpVPGnEC9mSri5E1_XZdS6NxL9uOe-3kk,5694
nibabel/tests/test_filebasedimages.py,sha256=6CxX0z6AyaGDtzw3UZbn5ADGtdcXcjG4O3v-PgToIrc,4375
nibabel/tests/test_filehandles.py,sha256=S84bR4QXgupvB7ya8PKib2AI7PEzjIcWVJAU0GoCdnE,1018
nibabel/tests/test_fileholders.py,sha256=PZLJGeaAFRvSIr_wJBPFpFhtdb7rEU-NeWY6cSUVuig,1414
nibabel/tests/test_filename_parser.py,sha256=cD0Dz9KGK3MAnZ8Jck9oGC01WXPbNcHtPSVU_MVaHKM,6605
nibabel/tests/test_files_interface.py,sha256=fVD7GvWEBwlKCPp47qyxmchUlUTbQT8VART5SNpLW-g,3807
nibabel/tests/test_fileslice.py,sha256=LjpVUxdmjACNDLNprCLoYseKEAsGshbMbJDrTp8RQSk,31715
nibabel/tests/test_fileutils.py,sha256=-cQnDeFS5g5jjnD7X08GyBzsZbmVAYy0GFoTAx5gUAs,1676
nibabel/tests/test_floating.py,sha256=L1gpuPNr-4_RpnDIMdGdCbQL4qwiJ99iowFkAW7hbMM,8996
nibabel/tests/test_funcs.py,sha256=Hu74259sHMA6IHgOZYc_QIA2XDUEgs7gLqINDtXK5x8,8177
nibabel/tests/test_image_api.py,sha256=-xqt7ZAoyqxK9JxBIpmfJ-AzsTtlW9YLlqActWdNw3w,30381
nibabel/tests/test_image_load_save.py,sha256=2LlBk_fZOgTYaPYnInyrAcylQHKpSBpLU5tEki85xSY,11853
nibabel/tests/test_image_types.py,sha256=PeQLYio9_tfZWgXurb0ebzNh7IjXhgASmY0e4wxsk80,5272
nibabel/tests/test_imageclasses.py,sha256=pSBXAzWhoDcuYvAIiRTkByz4PDh5Hekehuowh93ow1I,1396
nibabel/tests/test_imageglobals.py,sha256=CDAU3ds2pEO0vTSCSWnH88f6694hSfU5iEjbcwyvU3Q,672
nibabel/tests/test_imagestats.py,sha256=ZC5LC7BZSkR9LN7rbIWePQIYDMecgC_5cjfllGtEgSc,823
nibabel/tests/test_init.py,sha256=p9DNCbrbvTUjpPbirnwFnwNHLE587isUYjJqPSN7Jis,1898
nibabel/tests/test_loadsave.py,sha256=l-D_UepEMvzqxpbV0I9s1uWbEcpQGE640VAUItrKnFE,8479
nibabel/tests/test_minc1.py,sha256=HEeBv3fd7aBZBGIO8V02nlJ9htHaKnk7bAW9OlBuMV0,7203
nibabel/tests/test_minc2.py,sha256=5C1eQmyxeICw1YhUawxqfEcTynSnIC4z3wHqQMro6jQ,3914
nibabel/tests/test_minc2_data.py,sha256=_FoF9tU8Ww3zTLC52baSsTZYdnbjiKJIvmvuga0d2Kg,5615
nibabel/tests/test_mriutils.py,sha256=OB91l9RUXp6akj8oqTVFmv4DS2pbu1XVwIxcHJEI87g,1065
nibabel/tests/test_nibabel_data.py,sha256=UOwwZaaf9lOt5yaydnW9sefBUcSe7et0flaL9z_9Tp8,737
nibabel/tests/test_nifti1.py,sha256=gh38S0jcWy66NkJVnDzWjVjLTtqQYV75tyX2lGlJuQE,67732
nibabel/tests/test_nifti2.py,sha256=wpI3ehLML6CVI8t-Fg3SE8H9gUDSeHTKdhYtj9op2qI,3608
nibabel/tests/test_onetime.py,sha256=pT9CFIYBYWNm_BbU_3r2pg5FosI5Juds0cannAW0bkY,1060
nibabel/tests/test_openers.py,sha256=kxz8whxV4Y4n26jlTiG6fv6F5xQk5lzt5tnCtc_x_W0,17033
nibabel/tests/test_optpkg.py,sha256=A6bpOrRCt0TmI82SspaEQ5DNPmMZ6c2LOyPHddnTj_I,2653
nibabel/tests/test_orientations.py,sha256=o4IdAVtNABzmSIVZ3Pp-JAhaFd7ckkVrMDQIafwFgpE,11860
nibabel/tests/test_parrec.py,sha256=tK53dokZEtM5YjRiNZTJddS4yD4BkgE64bnxAv4ug-4,35364
nibabel/tests/test_parrec_data.py,sha256=uUMASCvwlabkoDf2OYExpWOwpxbYZjvA6kp0V0ySPmE,3361
nibabel/tests/test_pkg_info.py,sha256=-UJP78coSHTOLpbuK6lfLXwt35I_zDzKpIC6dCbnmrA,2231
nibabel/tests/test_pointset.py,sha256=7gNmcG-n_8zXuS_nmMwBsyKReOgOSFwC69ONxLpbfcE,6489
nibabel/tests/test_processing.py,sha256=jHz9m_T7SkvNYNcbLR_mdm33M2iN2xiOOF5QBHnWlbo,20663
nibabel/tests/test_proxy_api.py,sha256=1ysVQAR-yi4kSr0iOsIyWrjmZ40eD9Vk2_rWrg4Hc_w,16434
nibabel/tests/test_quaternions.py,sha256=HCKedifFMWmRZGnA0mKyQkPnAm3qzZ2f-kBBknM6rpM,6721
nibabel/tests/test_recoder.py,sha256=etU1e-NL4cjXDUolv3lNvHaMrtIudUQNeyhDiWkQnQ8,5603
nibabel/tests/test_removalschedule.py,sha256=0uxU0rHHTzwU7GbT-FnBr5yg_9K8mLZuXUvzD30hJU8,6141
nibabel/tests/test_round_trip.py,sha256=26C2i5rXywC5syASqD-18VWoA_uo7TikSZp1s9mOtUY,6854
nibabel/tests/test_rstutils.py,sha256=YFnMP9GDEqJYPm4-fGOsW4G-51c4NbLEbkRcOarfBxQ,3878
nibabel/tests/test_scaling.py,sha256=dJJByXzQcmwYPoAzENYz--AtZ2tZ0PvNPCzG5iHGlF8,8750
nibabel/tests/test_scripts.py,sha256=yfdmiSozy7bqjkA7jzuo4YuounsvfW1mfK8PAoPpgdU,20196
nibabel/tests/test_spaces.py,sha256=5T5L-tSdGDWm01NMCs21-w656t5x-wrcV54cv6M2hBk,4838
nibabel/tests/test_spatialimages.py,sha256=zXxM_l64rSvArC4YEe8j1wyrijRx4eNoGR6O20lIeQI,24457
nibabel/tests/test_spm2analyze.py,sha256=RsWTnZg6yfnFFBqonQ24wsuCULXCDeDli0fVsGI3cNI,2638
nibabel/tests/test_spm99analyze.py,sha256=pSaxF-Mxj-NFcA8_UomTQZ066Sh2xDPD5DuMgjY4NXQ,22619
nibabel/tests/test_testing.py,sha256=rzLarZn0oX7Ay6I8vrENCBmN4xiJQ-dJXpSAcReJ1KM,6492
nibabel/tests/test_tmpdirs.py,sha256=3hVfMgm93jnyyMaiYHOYftLB6HF5L4EkThLjfxyoczc,626
nibabel/tests/test_tripwire.py,sha256=k40sPTe_UbcoUhQMRgMfO97byRIA43mA8RLscuNWzc0,541
nibabel/tests/test_viewers.py,sha256=rmGAOqRFZOXDLjb1CoANYefgydmKG8UK_uA6cdlv_Gw,11512
nibabel/tests/test_volumeutils.py,sha256=9AjAXCtb5KFd8haXuzbZ9VWAJxuFJzLIYDl7CEh59Ho,52408
nibabel/tests/test_wrapstruct.py,sha256=GBE4T7_KOv_5SLjPUvF2wcDkyM7xI1Ap8hytQKMeoJQ,18597
nibabel/tmpdirs.py,sha256=Q2H899lkEwDADTVdw-W6HKbUHFqQVNVot70QWAiJzDg,3847
nibabel/tripwire.py,sha256=-48B095mXF_E-mKHP7IcrBvqDDRic5QpkQjF8AV4bko,1409
nibabel/viewers.py,sha256=ugAszOGBtaxZNe1juoOOAJ8IKzwOjrpPlxH3MkP_iKQ,20412
nibabel/volumeutils.py,sha256=kqS9GdZzB4aaVFml4zFVyi1pwe5RHNO35-ICZRbaIbs,51707
nibabel/wrapstruct.py,sha256=OEnNP45OSsrS-qfMoI0IzwQU79wk7mSwPHgYJZZznyU,16351
nibabel/xmlutils.py,sha256=NQAZyCZFC2akJqeQ95NeFHEShmjbTqgeciBLAWfO0CI,3938
